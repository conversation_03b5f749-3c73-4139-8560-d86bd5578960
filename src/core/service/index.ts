import type { LoginCredentials } from "~/auth/service/model/auth";
import { AuthUsecase } from "~/auth/service/model/usecase";
import type { CreateClient, UpdateClient } from "~/client/service/model/client";
import { ClientUsecase } from "~/modules/client/service/model/usecase";
import { WorkerUsecase } from "~/worker/service/model/usecase";
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, UpdateWorker } from "~/worker/service/model/worker";

const authService = {
	login: (credentials: LoginCredentials) => AuthUsecase.login(credentials),
	logout: () => AuthUsecase.logout(),
	getSession: () => AuthUsecase.getSession(),
};

const clientService = {
	create: (client: CreateClient) => ClientUsecase.create(client),
	getAll: () => ClientUsecase.getAll(),
	getById: (id: string) => ClientUsecase.getById(id),
	update: (client: UpdateClient) => ClientUsecase.update(client),
	delete: (id: string) => ClientUsecase.delete(id),
};

const workerService = {
	create: (worker: CreateWorker) => WorkerUsecase.create(worker),
	getAll: () => WorkerUsecase.getAll(),
	getById: (id: string) => WorkerUsecase.getById(id),
	update: (worker: UpdateWorker) => WorkerUsecase.update(worker),
	delete: (id: string) => WorkerUsecase.delete(id),
};

export const serviceRegistry = {
	auth: authService,
	client: clientService,
	worker: workerService,
};

export type serviceRegistry = typeof serviceRegistry;

import { queryOptions } from "@tanstack/react-query";
import type { serviceRegistry } from "~/core/service";
import { AppRuntime } from "~/core/service/utils/runtimes";

export const scheduleOptions = ({ schedule }: serviceRegistry) =>
	queryOptions({
		queryKey: ["schedules"],
		queryFn: () => AppRuntime.runPromise(schedule.getAll()),
	});

export const scheduleOptionsById = ({ schedule }: serviceRegistry, id: string) =>
	queryOptions({
		queryKey: ["schedules", id],
		queryFn: () => AppRuntime.runPromise(schedule.getById(id)),
	});

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import { type UpdateSchedule, Schedule } from "../service/model/schedule";
import { scheduleOptions } from "./schedule-options";

export default function useEditSchedule() {
	const service = useService();
	const { schedule } = service;
	const queryClient = useQueryClient();
	const queryKey = scheduleOptions(service).queryKey;

	return useMutation({
		mutationFn: (updatedSchedule: UpdateSchedule) =>
			AppRuntime.runPromise(schedule.update(updatedSchedule)),
		onSuccess: (_, updatedSchedule) => {
			queryClient.setQueryData(queryKey, (old: Schedule[] | undefined) =>
				create(old ?? [], (draft) => {
					const index = draft.findIndex((s) => s.id === updatedSchedule.id);
					if (index !== -1) {
						draft[index] = {
							...draft[index],
							name: updatedSchedule.name,
							sessionDuration: updatedSchedule.sessionDuration,
							breakDuration: updatedSchedule.breakDuration,
							turns: updatedSchedule.turns.map(turn => ({
								id: turn.id,
								name: turn.name,
								startTime: turn.startTime,
								endTime: turn.endTime,
								createdAt: draft[index].turns.find(t => t.id === turn.id)?.createdAt || null,
								updatedAt: new Date().toISOString(),
								deletedAt: null,
							})),
							updatedAt: new Date().toISOString(),
						};
					}
				}),
			);
		},
	});
}

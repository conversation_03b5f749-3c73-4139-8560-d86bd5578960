import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { Schedule, CreateSchedule } from "../service/model/schedule";
import { scheduleOptions } from "./schedule-options";

export default function useCreateSchedule() {
	const service = useService();
	const { schedule } = service;
	const queryClient = useQueryClient();
	const queryKey = scheduleOptions(service).queryKey;

	return useMutation({
		mutationFn: (newSchedule: CreateSchedule) =>
			AppRuntime.runPromise(schedule.create(newSchedule)),
		onSuccess: (id, newSchedule) => {
			queryClient.setQueryData(queryKey, (old: Schedule[] | undefined) =>
				create(old ?? [], (draft) => {
					draft.push({
						id,
						name: newSchedule.name,
						sessionDuration: newSchedule.sessionDuration,
						breakDuration: newSchedule.breakDuration,
						turns: newSchedule.turns.map(turn => ({
							id: "", // Will be set by backend
							name: turn.name,
							startTime: turn.startTime,
							endTime: turn.endTime,
							createdAt: new Date().toISOString(),
							updatedAt: null,
							deletedAt: null,
						})),
						createdAt: new Date().toISOString(),
						updatedAt: null,
						deletedAt: null,
					} as Schedule);
				}),
			);
		},
	});
}

import { useQuery } from "@tanstack/react-query";
import { Link } from "@tanstack/react-router";
import { ArrowLeft, Clock, Eye, FileText, Plus, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { useService } from "~/config/context/serviceProvider";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import { scheduleOptionsById } from "../../hooks/schedule-options";
import useEditSchedule from "../../hooks/use-edit-schedule";
import {
	CreateScheduleSchema,
	type TurnCreateSchema,
} from "../CreateScheduleModal/schema";
import SchedulePreview from "../SchedulePreview";

interface EditSchedulePageProps {
	id: string;
}

export default function EditSchedulePage({ id }: EditSchedulePageProps) {
	const [selectedTurnIndex, setSelectedTurnIndex] = useState<number | null>(
		null,
	);
	const svc = useService();
	const { mutate } = useEditSchedule();

	const {
		data: schedule,
		isLoading,
		error,
	} = useQuery(scheduleOptionsById(svc, id));

	const form = useAppForm({
		defaultValues: {
			name: "",
			sessionDuration: 60,
			breakDuration: 15,
			turns: [],
		} as CreateScheduleSchema,
		validators: {
			onChange: CreateScheduleSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					id,
					name: value.name,
					sessionDuration: value.sessionDuration,
					breakDuration: value.breakDuration,
					turns: value.turns.map((turn, index) => ({
						id: schedule?.turns[index]?.id || "",
						name: turn.name,
						startTime: turn.startTime,
						endTime: turn.endTime,
					})),
				},
				{
					onSuccess: () => {
						toast.success("Horario actualizado exitosamente");
						window.history.back();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	// Update form when schedule data is loaded
	useEffect(() => {
		if (schedule) {
			form.setFieldValue("name", schedule.name);
			form.setFieldValue("sessionDuration", schedule.sessionDuration);
			form.setFieldValue("breakDuration", schedule.breakDuration);
			form.setFieldValue(
				"turns",
				schedule.turns.map((turn) => ({
					name: turn.name,
					startTime: turn.startTime,
					endTime: turn.endTime,
				})),
			);
		}
	}, [schedule]);

	const addTurn = () => {
		const currentTurns = form.getFieldValue("turns") || [];
		form.setFieldValue("turns", [
			...currentTurns,
			{ name: "", startTime: 800, endTime: 1700 },
		]);
	};

	const removeTurn = (index: number) => {
		const currentTurns = form.getFieldValue("turns") || [];
		form.setFieldValue(
			"turns",
			currentTurns.filter((_, i) => i !== index),
		);
		if (selectedTurnIndex === index) {
			setSelectedTurnIndex(null);
		} else if (selectedTurnIndex !== null && selectedTurnIndex > index) {
			setSelectedTurnIndex(selectedTurnIndex - 1);
		}
	};

	const updateTurn = (
		index: number,
		field: keyof TurnCreateSchema,
		value: string | number,
	) => {
		const currentTurns = form.getFieldValue("turns") || [];
		const updatedTurns = [...currentTurns];
		updatedTurns[index] = {
			...updatedTurns[index],
			[field]: value,
		} as TurnCreateSchema;
		form.setFieldValue("turns", updatedTurns);
	};

	const formatTime = (time: number) => {
		const hours = Math.floor(time / 100);
		const minutes = time % 100;
		return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
	};

	const parseTime = (timeString: string) => {
		const [hours, minutes] = timeString.split(":").map(Number);
		return (hours || 0) * 100 + (minutes || 0);
	};

	if (isLoading) {
		return (
			<div className="container mx-auto max-w-6xl">
				<div className="flex h-64 items-center justify-center">
					<span className="loading loading-spinner loading-lg" />
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="container mx-auto max-w-6xl">
				<div className="alert alert-error">
					<span>Error: {getErrorResult(error).error.message}</span>
				</div>
			</div>
		);
	}

	const turns = form.getFieldValue("turns") || [];
	const selectedTurn =
		selectedTurnIndex !== null ? turns[selectedTurnIndex] : null;

	return (
		<div className="container mx-auto max-w-6xl">
			<div className="mb-6">
				<Link to="/admin/schedules" className="btn btn-ghost">
					<ArrowLeft size={16} />
					Volver a Horarios
				</Link>
			</div>

			<div className="card bg-base-300">
				<div className="card-body">
					<h2 className="card-title mb-6 text-2xl">Editar Horario</h2>

					<form
						onSubmit={(e) => {
							e.preventDefault();
							form.handleSubmit();
						}}
					>
						<form.AppForm>
							<div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
								{/* Left Column - Form */}
								<div className="space-y-6">
									<fieldset className="fieldset">
										<form.AppField
											name="name"
											children={({ FSTextField }) => (
												<FSTextField
													label="Nombre del Horario"
													placeholder="Nombre del Horario"
													prefixComponent={<FileText size={16} />}
												/>
											)}
										/>
										<form.AppField
											name="sessionDuration"
											children={({ FSTextField }) => (
												<FSTextField
													label="Duración de Sesión (minutos)"
													placeholder="60"
													type="number"
													prefixComponent={<Clock size={16} />}
												/>
											)}
										/>
										<form.AppField
											name="breakDuration"
											children={({ FSTextField }) => (
												<FSTextField
													label="Duración de Descanso (minutos)"
													placeholder="15"
													type="number"
													prefixComponent={<Clock size={16} />}
												/>
											)}
										/>
									</fieldset>

									{/* Turns Table */}
									<div className="form-control">
										<label className="label">
											<span className="label-text font-semibold">Turnos</span>
											<button
												type="button"
												className="btn btn-sm btn-primary"
												onClick={addTurn}
											>
												<Plus size={16} />
												Agregar Turno
											</button>
										</label>

										<div className="overflow-x-auto">
											<table className="table-zebra table">
												<thead>
													<tr>
														<th>Nombre</th>
														<th>Hora Inicio</th>
														<th>Hora Fin</th>
														<th>Acciones</th>
													</tr>
												</thead>
												<tbody>
													{turns.map((turn, index) => (
														<tr
															key={index}
															className={
																selectedTurnIndex === index
																	? "bg-primary/20"
																	: ""
															}
														>
															<td>
																<input
																	type="text"
																	className="input input-sm w-full"
																	value={turn.name}
																	onChange={(e) =>
																		updateTurn(index, "name", e.target.value)
																	}
																	placeholder="Nombre del turno"
																/>
															</td>
															<td>
																<input
																	type="time"
																	className="input input-sm w-full"
																	value={formatTime(turn.startTime)}
																	onChange={(e) =>
																		updateTurn(
																			index,
																			"startTime",
																			parseTime(e.target.value),
																		)
																	}
																/>
															</td>
															<td>
																<input
																	type="time"
																	className="input input-sm w-full"
																	value={formatTime(turn.endTime)}
																	onChange={(e) =>
																		updateTurn(
																			index,
																			"endTime",
																			parseTime(e.target.value),
																		)
																	}
																/>
															</td>
															<td>
																<div className="flex gap-2">
																	<button
																		type="button"
																		className={`btn btn-sm ${selectedTurnIndex === index ? "btn-primary" : "btn-outline"}`}
																		onClick={() =>
																			setSelectedTurnIndex(
																				selectedTurnIndex === index
																					? null
																					: index,
																			)
																		}
																	>
																		<Eye size={16} />
																	</button>
																	<button
																		type="button"
																		className="btn btn-sm btn-error"
																		onClick={() => removeTurn(index)}
																	>
																		<Trash2 size={16} />
																	</button>
																</div>
															</td>
														</tr>
													))}
												</tbody>
											</table>
										</div>
									</div>

									<div className="flex gap-4">
										<button type="submit" className="btn btn-primary">
											Actualizar Horario
										</button>
										<Link to="/admin/schedules" className="btn btn-outline">
											Cancelar
										</Link>
									</div>
								</div>

								{/* Right Column - Schedule Preview */}
								<div className="space-y-6">
									<h3 className="font-semibold text-xl">
										Vista Previa del Horario
									</h3>
									{selectedTurn ? (
										<SchedulePreview
											turn={selectedTurn}
											sessionDuration={
												form.getFieldValue("sessionDuration") || 60
											}
											breakDuration={form.getFieldValue("breakDuration") || 15}
										/>
									) : (
										<div className="card bg-base-200">
											<div className="card-body text-center">
												<p className="text-base-content/70">
													Selecciona un turno para ver la vista previa del
													horario
												</p>
											</div>
										</div>
									)}
								</div>
							</div>
						</form.AppForm>
					</form>
				</div>
			</div>
		</div>
	);
}

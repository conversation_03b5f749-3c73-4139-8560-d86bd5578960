import type { TurnCreateSchema } from "../CreateScheduleModal/schema";

interface SchedulePreviewProps {
	turn: TurnCreateSchema;
	sessionDuration: number;
	breakDuration: number;
}

export default function SchedulePreview({ turn, sessionDuration, breakDuration }: SchedulePreviewProps) {
	const formatTime = (time: number) => {
		const hours = Math.floor(time / 100);
		const minutes = time % 100;
		return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
	};

	const generateTimeSlots = () => {
		const startHour = Math.floor(turn.startTime / 100);
		const startMinute = turn.startTime % 100;
		const endHour = Math.floor(turn.endTime / 100);
		const endMinute = turn.endTime % 100;

		const startTimeInMinutes = startHour * 60 + startMinute;
		const endTimeInMinutes = endHour * 60 + endMinute;
		
		const slots = [];
		let currentTime = startTimeInMinutes;
		
		while (currentTime < endTimeInMinutes) {
			const sessionEnd = currentTime + sessionDuration;
			const breakEnd = sessionEnd + breakDuration;
			
			// Add session slot
			if (sessionEnd <= endTimeInMinutes) {
				slots.push({
					type: 'session',
					start: currentTime,
					end: sessionEnd,
					label: 'Sesión'
				});
			}
			
			// Add break slot if there's time and it's not the last session
			if (breakEnd <= endTimeInMinutes && breakDuration > 0) {
				slots.push({
					type: 'break',
					start: sessionEnd,
					end: breakEnd,
					label: 'Descanso'
				});
				currentTime = breakEnd;
			} else {
				currentTime = sessionEnd;
			}
		}
		
		return slots;
	};

	const timeSlots = generateTimeSlots();
	const days = ['Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado', 'Domingo'];

	const minutesToTimeString = (minutes: number) => {
		const hours = Math.floor(minutes / 60);
		const mins = minutes % 60;
		return `${hours.toString().padStart(2, "0")}:${mins.toString().padStart(2, "0")}`;
	};

	return (
		<div className="card bg-base-100">
			<div className="card-body">
				<h4 className="card-title text-lg">
					{turn.name || "Turno sin nombre"} - {formatTime(turn.startTime)} a {formatTime(turn.endTime)}
				</h4>
				
				<div className="overflow-x-auto">
					<table className="table table-xs">
						<thead>
							<tr>
								<th className="w-20">Hora</th>
								{days.map(day => (
									<th key={day} className="text-center">{day}</th>
								))}
							</tr>
						</thead>
						<tbody>
							{timeSlots.map((slot, index) => (
								<tr key={index}>
									<td className="font-mono text-xs">
										{minutesToTimeString(slot.start)} - {minutesToTimeString(slot.end)}
									</td>
									{days.map(day => (
										<td key={day} className="text-center p-1">
											<div 
												className={`
													rounded px-2 py-1 text-xs font-medium
													${slot.type === 'session' 
														? 'bg-primary text-primary-content' 
														: 'bg-secondary text-secondary-content'
													}
												`}
											>
												{slot.label}
											</div>
										</td>
									))}
								</tr>
							))}
						</tbody>
					</table>
				</div>

				<div className="mt-4 text-sm">
					<div className="flex gap-4">
						<div className="flex items-center gap-2">
							<div className="w-4 h-4 bg-primary rounded"></div>
							<span>Sesión ({sessionDuration} min)</span>
						</div>
						{breakDuration > 0 && (
							<div className="flex items-center gap-2">
								<div className="w-4 h-4 bg-secondary rounded"></div>
								<span>Descanso ({breakDuration} min)</span>
							</div>
						)}
					</div>
					<div className="mt-2 text-xs text-base-content/70">
						Total de sesiones: {timeSlots.filter(slot => slot.type === 'session').length}
					</div>
				</div>
			</div>
		</div>
	);
}

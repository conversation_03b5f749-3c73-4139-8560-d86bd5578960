import * as v from "valibot";

export const TurnCreateSchema = v.object({
	name: v.pipe(
		v.string("Debe ingresar un nombre para el turno"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	startTime: v.pipe(
		v.number("Debe ingresar una hora de inicio"),
		v.minValue(0, "La hora debe ser mayor o igual a 0"),
		v.maxVal<PERSON>(2359, "La hora debe ser menor o igual a 23:59"),
	),
	endTime: v.pipe(
		v.number("Debe ingresar una hora de fin"),
		v.minValue(0, "La hora debe ser mayor o igual a 0"),
		v.maxValue(2359, "La hora debe ser menor o igual a 23:59"),
	),
});

export const CreateScheduleSchema = v.object({
	name: v.pipe(
		v.string("Debe ingresar un nombre"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	sessionDuration: v.pipe(
		v.number("Debe ingresar la duración de la sesión"),
		v.minValue(1, "La duración debe ser mayor a 0"),
	),
	breakDuration: v.pipe(
		v.number("Debe ingresar la duración del descanso"),
		v.minValue(0, "La duración debe ser mayor o igual a 0"),
	),
	turns: v.array(TurnCreateSchema),
});

export type CreateScheduleSchema = v.InferOutput<typeof CreateScheduleSchema>;
export type TurnCreateSchema = v.InferOutput<typeof TurnCreateSchema>;

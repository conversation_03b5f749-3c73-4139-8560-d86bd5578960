import { Clock, FileText, Plus, Trash2 } from "lucide-react";
import CloseModal from "~/core/components/CloseModal";
import { cn } from "~/core/utils/classes";
import type { CreateScheduleModalProps } from "./use-create-modal";
import useCreateScheduleModal from "./use-create-modal";

export default function CreateScheduleModal({
	isOpen,
	setIsOpen,
}: CreateScheduleModalProps) {
	const { form, handleClose } = useCreateScheduleModal({ isOpen, setIsOpen });

	const addTurn = () => {
		const currentTurns = form.getFieldValue("turns") || [];
		form.setFieldValue("turns", [
			...currentTurns,
			{ name: "", startTime: 800, endTime: 1700 },
		]);
	};

	const removeTurn = (index: number) => {
		const currentTurns = form.getFieldValue("turns") || [];
		form.setFieldValue(
			"turns",
			currentTurns.filter((_, i) => i !== index),
		);
	};

	const formatTime = (time: number) => {
		const hours = Math.floor(time / 100);
		const minutes = time % 100;
		return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
	};

	const parseTime = (timeString: string) => {
		const [hours, minutes] = timeString.split(":").map(Number);
		return hours * 100 + minutes;
	};

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box max-w-2xl">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">Crear Horario</h3>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						<fieldset className="fieldset">
							<form.AppField
								name="name"
								children={({ FSTextField }) => (
									<FSTextField
										label="Nombre del Horario"
										placeholder="Nombre del Horario"
										prefixComponent={<FileText size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="sessionDuration"
								children={({ FSTextField }) => (
									<FSTextField
										label="Duración de Sesión (minutos)"
										placeholder="60"
										type="number"
										prefixComponent={<Clock size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="breakDuration"
								children={({ FSTextField }) => (
									<FSTextField
										label="Duración de Descanso (minutos)"
										placeholder="15"
										type="number"
										prefixComponent={<Clock size={16} />}
									/>
								)}
							/>

							<div className="form-control">
								<label className="label">
									<span className="label-text">Turnos</span>
								</label>
								<div className="space-y-4">
									{(form.getFieldValue("turns") || []).map((turn, index) => (
										<div key={index} className="card bg-base-200 p-4">
											<div className="flex items-center justify-between mb-2">
												<h4 className="font-semibold">Turno {index + 1}</h4>
												<button
													type="button"
													className="btn btn-sm btn-error"
													onClick={() => removeTurn(index)}
												>
													<Trash2 size={16} />
												</button>
											</div>
											<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
												<form.AppField
													name={`turns.${index}.name`}
													children={({ FSTextField }) => (
														<FSTextField
															label="Nombre"
															placeholder="Mañana"
														/>
													)}
												/>
												<form.AppField
													name={`turns.${index}.startTime`}
													children={({ FSTextField }) => (
														<FSTextField
															label="Hora de Inicio"
															type="time"
															value={formatTime(turn.startTime || 800)}
															onChange={(e) => {
																const timeValue = parseTime(e.target.value);
																form.setFieldValue(`turns.${index}.startTime`, timeValue);
															}}
														/>
													)}
												/>
												<form.AppField
													name={`turns.${index}.endTime`}
													children={({ FSTextField }) => (
														<FSTextField
															label="Hora de Fin"
															type="time"
															value={formatTime(turn.endTime || 1700)}
															onChange={(e) => {
																const timeValue = parseTime(e.target.value);
																form.setFieldValue(`turns.${index}.endTime`, timeValue);
															}}
														/>
													)}
												/>
											</div>
										</div>
									))}
									<button
										type="button"
										className="btn btn-outline btn-primary w-full"
										onClick={addTurn}
									>
										<Plus size={16} />
										Agregar Turno
									</button>
								</div>
							</div>
						</fieldset>
						<div className="modal-action">
							<button type="submit" className="btn btn-primary">
								Crear
							</button>
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}

import { HttpBody } from "@effect/platform";
import { Effect, Layer, Schema } from "effect";
import { ApiHttpClient } from "~/core/service/repo/api";
import { handleDResponse, handleResponse } from "~/core/service/repo/api/utils";
import { ScheduleRepository } from "../../model/repository";
import type { CreateSchedule, UpdateSchedule, Schedule } from "../../model/schedule";
import {
	CreateScheduleApiFromCreateSchedule,
	CreateScheduleApiResponse,
	UpdateScheduleApiFromUpdateSchedule,
	ScheduleFromApi,
	ScheduleListFromApi,
} from "./dto";

const baseUrl = "/v1/schedules";

const makeScheduleApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(ScheduleListFromApi))),
		getById: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}`)
				.pipe(Effect.flatMap(handleDResponse(ScheduleFromApi))),
		create: (schedule: CreateSchedule) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(CreateScheduleApiFromCreateSchedule)(schedule),
					),
				})
				.pipe(Effect.flatMap(handleDResponse(CreateScheduleApiResponse))),
		update: (schedule: UpdateSchedule) =>
			httpClient
				.put(`${baseUrl}/${schedule.id}`, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(UpdateScheduleApiFromUpdateSchedule)(schedule),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		delete: (id: string) =>
			httpClient.del(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleResponse)),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const scheduleApiRepoLive = Layer.effect(
	ScheduleRepository,
	makeScheduleApiRepo,
);

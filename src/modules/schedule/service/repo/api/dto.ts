import { Schema } from "effect";
import { CreateSchedule, UpdateSchedule, Schedule, Turn, TurnCreate, TurnUpdate } from "../../model/schedule";

export const TurnApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	start_time: Schema.Number,
	end_time: Schema.Number,
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const TurnFromApi = Schema.transform(
	TurnApi,
	Turn,
	{
		strict: true,
		encode: (turn) => ({
			id: turn.id,
			name: turn.name,
			start_time: turn.startTime,
			end_time: turn.endTime,
			created_at: turn.createdAt,
			updated_at: turn.updatedAt,
			deleted_at: turn.deletedAt,
		}),
		decode: (turnApi) => ({
			id: turnApi.id,
			name: turnApi.name,
			startTime: turnApi.start_time,
			endTime: turnApi.end_time,
			createdAt: turnApi.created_at,
			updatedAt: turnApi.updated_at,
			deletedAt: turnApi.deleted_at,
		}),
	},
);

export const ScheduleApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	session_duration: Schema.Number,
	break_duration: Schema.Number,
	turns: Schema.mutable(Schema.Array(TurnApi)),
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const ScheduleFromApi = Schema.transform(
	ScheduleApi,
	Schedule,
	{
		strict: true,
		encode: (schedule) => ({
			id: schedule.id,
			name: schedule.name,
			session_duration: schedule.sessionDuration,
			break_duration: schedule.breakDuration,
			turns: schedule.turns.map(turn => Schema.encodeUnknownSync(TurnFromApi)(turn)),
			created_at: schedule.createdAt,
			updated_at: schedule.updatedAt,
			deleted_at: schedule.deletedAt,
		}),
		decode: (scheduleApi) => ({
			id: scheduleApi.id,
			name: scheduleApi.name,
			sessionDuration: scheduleApi.session_duration,
			breakDuration: scheduleApi.break_duration,
			turns: scheduleApi.turns.map(turnApi => Schema.decodeUnknownSync(TurnFromApi)(turnApi)),
			createdAt: scheduleApi.created_at,
			updatedAt: scheduleApi.updated_at,
			deletedAt: scheduleApi.deleted_at,
		}),
	},
);

export const ScheduleListFromApi = Schema.Array(ScheduleFromApi);

export const TurnCreateApi = Schema.Struct({
	name: Schema.String,
	start_time: Schema.Number,
	end_time: Schema.Number,
});

export const TurnCreateApiFromTurnCreate = Schema.transform(
	TurnCreate,
	TurnCreateApi,
	{
		strict: true,
		encode: (turnCreate) => ({
			name: turnCreate.name,
			start_time: turnCreate.startTime,
			end_time: turnCreate.endTime,
		}),
		decode: (turnCreateApi) => ({
			name: turnCreateApi.name,
			startTime: turnCreateApi.start_time,
			endTime: turnCreateApi.end_time,
		}),
	},
);

export const CreateScheduleApi = Schema.Struct({
	name: Schema.String,
	session_duration: Schema.Number,
	break_duration: Schema.Number,
	turns: Schema.mutable(Schema.Array(TurnCreateApi)),
});

export const CreateScheduleApiFromCreateSchedule = Schema.transform(
	CreateSchedule,
	CreateScheduleApi,
	{
		strict: true,
		encode: (createSchedule) => ({
			name: createSchedule.name,
			session_duration: createSchedule.sessionDuration,
			break_duration: createSchedule.breakDuration,
			turns: createSchedule.turns.map(turn => Schema.encodeUnknownSync(TurnCreateApiFromTurnCreate)(turn)),
		}),
		decode: (createScheduleApi) => ({
			name: createScheduleApi.name,
			sessionDuration: createScheduleApi.session_duration,
			breakDuration: createScheduleApi.break_duration,
			turns: createScheduleApi.turns.map(turnApi => Schema.decodeUnknownSync(TurnCreateApiFromTurnCreate)(turnApi)),
		}),
	},
);

export const CreateScheduleApiResponse = Schema.String;

export const TurnUpdateApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	start_time: Schema.Number,
	end_time: Schema.Number,
});

export const TurnUpdateApiFromTurnUpdate = Schema.transform(
	TurnUpdate,
	TurnUpdateApi,
	{
		strict: true,
		encode: (turnUpdate) => ({
			id: turnUpdate.id,
			name: turnUpdate.name,
			start_time: turnUpdate.startTime,
			end_time: turnUpdate.endTime,
		}),
		decode: (turnUpdateApi) => ({
			id: turnUpdateApi.id,
			name: turnUpdateApi.name,
			startTime: turnUpdateApi.start_time,
			endTime: turnUpdateApi.end_time,
		}),
	},
);

export const UpdateScheduleApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	session_duration: Schema.Number,
	break_duration: Schema.Number,
	turns: Schema.mutable(Schema.Array(TurnUpdateApi)),
});

export const UpdateScheduleApiFromUpdateSchedule = Schema.transform(
	UpdateSchedule,
	UpdateScheduleApi,
	{
		strict: true,
		encode: (updateSchedule) => ({
			id: updateSchedule.id,
			name: updateSchedule.name,
			session_duration: updateSchedule.sessionDuration,
			break_duration: updateSchedule.breakDuration,
			turns: updateSchedule.turns.map(turn => Schema.encodeUnknownSync(TurnUpdateApiFromTurnUpdate)(turn)),
		}),
		decode: (updateScheduleApi) => ({
			id: updateScheduleApi.id,
			name: updateScheduleApi.name,
			sessionDuration: updateScheduleApi.session_duration,
			breakDuration: updateScheduleApi.break_duration,
			turns: updateScheduleApi.turns.map(turnApi => Schema.decodeUnknownSync(TurnUpdateApiFromTurnUpdate)(turnApi)),
		}),
	},
);

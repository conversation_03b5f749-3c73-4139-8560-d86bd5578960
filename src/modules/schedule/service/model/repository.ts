import { Effect } from "effect";
import type { AppError } from "~/core/service/model/error";
import type { CreateSchedule, UpdateSchedule, Schedule } from "./schedule";

export class ScheduleRepository extends Effect.Tag("ScheduleRepository")<
	ScheduleRepository,
	{
		readonly getAll: () => Effect.Effect<Schedule[], AppError>;
		readonly getById: (id: string) => Effect.Effect<Schedule, AppError>;
		readonly create: (schedule: CreateSchedule) => Effect.Effect<string, AppError>;
		readonly update: (schedule: UpdateSchedule) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
	}
>() {}

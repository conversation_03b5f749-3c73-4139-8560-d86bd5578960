import { useNavigate } from "@tanstack/react-router";
import { User } from "lucide-react";
import { useLogout } from "../hooks/use-logout";

export default function Navbar() {
	const { mutate } = useLogout();
	const navigate = useNavigate();

	const handleLogout = () => {
		mutate(undefined, {
			onSettled: () => {
				navigate({
					to: "/login",
				});
			},
		});
	};

	return (
		<div className="navbar w-full bg-neutral text-neutral-content">
			<div className="flex-1">
				<button type="button" className="btn btn-ghost text-xl">
					Schedhold
				</button>
			</div>
			<div className="flex-none">
				<details className="dropdown dropdown-end">
					<summary className="btn btn-circle avatar">
						<User size={26} />
					</summary>
					<ul className="menu menu-sm dropdown-content z-[1] mt-3 w-52 rounded-box bg-neutral p-2 shadow">
						<li>
							<span>Profile</span>
						</li>
						<li>
							<span>Settings</span>
						</li>
						<li>
							<button type="button" onClick={handleLogout}>
								Logout
							</button>
						</li>
					</ul>
				</details>
			</div>
		</div>
	);
}
